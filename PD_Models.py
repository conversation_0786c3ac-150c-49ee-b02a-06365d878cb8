import matplotlib.pyplot as plt
import numpy as np
from docx import Document
from docx.shared import Inches
import io
import base64

# Create a function to save matplotlib figures as images
def save_matplotlib_as_image(fig):
    buf = io.BytesIO()
    fig.savefig(buf, format='png', dpi=150, bbox_inches='tight')
    buf.seek(0)
    return buf

# Generate Figure 1: Hyperbolic Emax Relationship
def generate_figure1():
    fig, ax = plt.subplots(figsize=(6, 4))
    
    # Parameters
    E0 = 10
    Emax = 90
    EC50 = 10
    
    # Concentration range
    C = np.linspace(0, 100, 1000)
    
    # Emax model
    E = E0 + (Emax * C) / (EC50 + C)
    
    # Linear approximation for low concentrations
    C_linear = np.linspace(0, 5, 100)
    E_linear = E0 + (Emax/EC50) * C_linear
    
    # Plot
    ax.plot(C, E, 'b-', linewidth=2, label='Emax Model')
    ax.plot(C_linear, E_linear, 'r--', linewidth=1.5, label='Linear Approximation')
    ax.axhline(y=E0, color='k', linestyle='-', alpha=0.3)
    ax.axhline(y=E0+Emax, color='k', linestyle='-', alpha=0.3)
    ax.axvline(x=EC50, color='g', linestyle='--', alpha=0.5)
    ax.plot(EC50, E0 + Emax/2, 'go')
    
    # Labels and annotations
    ax.set_xlabel('Concentration (C)', fontsize=12)
    ax.set_ylabel('Effect (E)', fontsize=12)
    ax.set_title('Hyperbolic Emax Relationship', fontsize=14)
    ax.text(5, E0+5, 'E₀', fontsize=12)
    ax.text(5, E0+Emax-5, 'E₀ + Eₘₐₓ', fontsize=12)
    ax.text(EC50+2, E0 + Emax/2, 'EC₅₀', fontsize=12)
    ax.legend(loc='lower right')
    ax.grid(True, alpha=0.3)
    
    return fig

# Generate Figure 2: Sigmoid Emax Curves
def generate_figure2():
    fig, ax = plt.subplots(figsize=(6, 4))
    
    # Parameters
    E0 = 10
    Emax = 90
    EC50 = 10
    
    # Concentration range (log scale)
    C = np.logspace(-1, 2, 1000)
    
    # Hill coefficients
    hill_coeffs = [1, 2, 4]
    colors = ['b', 'g', 'r']
    labels = ['n=1', 'n=2', 'n=4']
    
    for n, color, label in zip(hill_coeffs, colors, labels):
        E = E0 + (Emax * C**n) / (EC50**n + C**n)
        ax.plot(C, E, color=color, linewidth=2, label=label)
    
    # Labels
    ax.set_xlabel('Concentration (C) [log scale]', fontsize=12)
    ax.set_ylabel('Effect (E)', fontsize=12)
    ax.set_title('Sigmoid Emax Curves with Different Hill Coefficients', fontsize=14)
    ax.set_xscale('log')
    ax.legend(loc='lower right')
    ax.grid(True, alpha=0.3)
    
    return fig

# Generate Figure 3: Biophase Model Simulation
def generate_figure3():
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Time parameters
    t = np.linspace(0, 10, 1000)
    k10 = 0.5  # Elimination rate constant
    ke0 = 1.0  # Effect compartment rate constant
    
    # Initial concentration after bolus
    Cp0 = 100
    Cp = Cp0 * np.exp(-k10 * t)
    
    # Effect compartment concentration
    Ce = np.zeros_like(t)
    for i in range(1, len(t)):
        dt = t[i] - t[i-1]
        dCe = ke0 * (Cp[i] - Ce[i-1]) * dt
        Ce[i] = Ce[i-1] + dCe
    
    # Effect calculation (Emax model)
    E0 = 10
    Emax = 90
    EC50 = 20
    E = E0 + (Emax * Ce) / (EC50 + Ce)
    
    # Plot 1: Time courses
    ax1.plot(t, Cp, 'b-', linewidth=2, label='Plasma (Cp)')
    ax1.plot(t, Ce, 'g-', linewidth=2, label='Effect-site (Ce)')
    ax1.plot(t, E, 'r-', linewidth=2, label='Effect')
    ax1.set_xlabel('Time', fontsize=12)
    ax1.set_ylabel('Concentration / Effect', fontsize=12)
    ax1.set_title('Time Courses', fontsize=14)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Hysteresis loop
    ax2.plot(Cp, E, 'b-', linewidth=2)
    ax2.set_xlabel('Plasma Concentration (Cp)', fontsize=12)
    ax2.set_ylabel('Effect', fontsize=12)
    ax2.set_title('Hysteresis Loop', fontsize=14)
    ax2.grid(True, alpha=0.3)
    
    # Add arrow to show direction
    idx = np.argmax(E)
    ax2.annotate('', xy=(Cp[idx], E[idx]), xytext=(Cp[idx-10], E[idx-10]),
                 arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    
    return fig

# Generate Figure 4: Indirect Response Model
def generate_figure4():
    fig, ax = plt.subplots(figsize=(6, 4))
    
    # Time parameters
    t = np.linspace(0, 24, 1000)
    
    # Drug concentration (single dose)
    ka = 1.0
    ke = 0.2
    dose = 100
    C = (dose * ka * ka) / (ka - ke) * (np.exp(-ke * t) - np.exp(-ka * t))
    
    # Response parameters
    kin = 10
    kout = 0.5
    R0 = kin / kout
    Imax = 0.8
    IC50 = 20
    
    # Simulate response
    R = np.zeros_like(t)
    R[0] = R0
    dt = t[1] - t[0]
    
    for i in range(1, len(t)):
        I = 1 - (Imax * C[i]) / (IC50 + C[i])
        dR = (kin * I - kout * R[i-1]) * dt
        R[i] = R[i-1] + dR
    
    # Plot
    ax2 = ax.twinx()
    ax.plot(t, C, 'b-', linewidth=2, label='Concentration')
    ax2.plot(t, R, 'r-', linewidth=2, label='Response')
    
    # Labels
    ax.set_xlabel('Time (hours)', fontsize=12)
    ax.set_ylabel('Concentration', color='b', fontsize=12)
    ax2.set_ylabel('Response', color='r', fontsize=12)
    ax.set_title('Indirect Response Model (Model I)', fontsize=14)
    ax.tick_params(axis='y', labelcolor='b')
    ax2.tick_params(axis='y', labelcolor='r')
    ax.grid(True, alpha=0.3)
    
    # Add baseline
    ax2.axhline(y=R0, color='r', linestyle='--', alpha=0.5)
    
    return fig

# Generate Figure 5: Transit Compartment Model
def generate_figure5():
    fig, ax = plt.subplots(figsize=(6, 4))
    
    # Time parameters
    t = np.linspace(0, 21, 1000)
    
    # Drug concentration (chemotherapy)
    ka = 0.5
    ke = 0.1
    dose = 100
    C = (dose * ka * ka) / (ka - ke) * (np.exp(-ke * t) - np.exp(-ka * t))
    
    # Transit model parameters
    n = 3  # Number of transit compartments
    MTT = 5  # Mean transit time
    ktr = n / MTT  # Transit rate constant
    Circ0 = 100  # Baseline neutrophil count
    Emax = 0.9  # Maximal inhibition
    EC50 = 10  # Concentration for half-maximal effect
    
    # Simulate transit compartments
    P = np.zeros((n, len(t)))
    Circ = np.zeros_like(t)
    Circ[0] = Circ0
    dt = t[1] - t[0]
    
    for i in range(1, len(t)):
        # Drug effect
        E = (Emax * C[i]) / (EC50 + C[i])
        
        # Proliferation compartment
        dP0 = ktr * (Circ0 * (1 - E) - P[0, i-1]) * dt
        P[0, i] = P[0, i-1] + dP0
        
        # Transit compartments
        for j in range(1, n):
            dPj = ktr * (P[j-1, i] - P[j, i-1]) * dt
            P[j, i] = P[j, i-1] + dPj
        
        # Circulating compartment
        dCirc = ktr * (P[n-1, i] - Circ[i-1]) * dt
        Circ[i] = Circ[i-1] + dCirc
    
    # Plot
    ax.plot(t, C, 'b-', linewidth=2, label='Drug Concentration')
    ax.plot(t, Circ, 'r-', linewidth=2, label='Neutrophil Count')
    
    # Labels
    ax.set_xlabel('Time (days)', fontsize=12)
    ax.set_ylabel('Concentration / Count', fontsize=12)
    ax.set_title('Transit Compartment Model of Neutropenia', fontsize=14)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Add baseline
    ax.axhline(y=Circ0, color='r', linestyle='--', alpha=0.5)
    
    return fig

# Create Word document
doc = Document()

# Add title
doc.add_heading('Pharmacodynamic Models and Their Clinical Applications', 0)

# Add introduction
doc.add_heading('Introduction', level=1)
intro = """
Pharmacodynamics (PD) describes the relationship between drug exposure and observed effect. It is distinct from pharmacokinetics (PK), which describes how the body handles a drug (absorption, distribution, metabolism and excretion), although the two disciplines are tightly linked. The PD response does not increase in a straight-line fashion with concentration; doubling the concentration rarely produces a doubled response[1]. Instead, the concentration–effect relationship is typically hyperbolic or sigmoidal and saturates at a maximum when all receptors or downstream signalling pathways are occupied or activated. PD models quantify this relationship and provide a framework for predicting the time course of effect from a known concentration–time profile. They are used in drug development to translate pre-clinical findings to humans, in clinical pharmacology to optimise dosing in individual patients and in regulatory science to evaluate benefit–risk.

This document introduces the major classes of pharmacodynamic models using mathematical expressions, illustrates their behaviour with figures and summarises clinical examples. A basic understanding of differential equations is helpful but not essential; where more advanced mathematics is used, the derivations are explained in plain language. Tables at the end of each section summarise key parameters and applications.
"""
doc.add_paragraph(intro)

# Add Section 1
doc.add_heading('1. Direct (Immediate) Response Models', level=1)

# Add Section 1.1
doc.add_heading('1.1 The Emax Model', level=2)
emax_text = """
The simplest PD model assumes that the drug acts directly on its target and that the effect is proportional to the fraction of receptors occupied. When receptor occupancy follows the law of mass action, the response can be described by the hyperbolic Emax model[2]. If $E_0$ is the baseline effect in the absence of drug, $E_{max}$ is the maximum effect above baseline and $EC_{50}$ is the concentration producing half of $E_{max}$, then the effect at concentration $C$ is

$$E = E_0 + \\frac{E_{max} \\times C}{EC_{50} + C} \\quad \\text{(Equation 1)}$$

Equation (1) is hyperbolic; at low concentrations ($C \\ll EC_{50}$) the response is approximately linear ($E \\approx E_0 + \\frac{E_{max}}{EC_{50}} \\times C$). At high concentrations ($C \\gg EC_{50}$) the response approaches $E_0 + E_{max}$.
"""
doc.add_paragraph(emax_text)

# Add Figure 1
fig1 = generate_figure1()
img_stream = save_matplotlib_as_image(fig1)
doc.add_picture(img_stream, width=Inches(6))
doc.add_paragraph("Figure 1: Hyperbolic Emax relationship showing that the effect increases rapidly at low concentrations but gradually approaches a maximum. The dashed line represents the linear approximation when C ≪ EC₅₀.")

# Continue with the rest of the document content...
# (I'll include the full document content below)

# Add Section 1.2
doc.add_heading('1.2 The Sigmoid Emax (Hill) Model', level=2)
sigmoid_text = """
For some drugs the concentration–effect curve is steeper or shallower than predicted by the simple Emax model. This happens when a drug binds cooperatively to multiple receptor sites or when there is allosteric modulation. A more flexible version of the Emax model introduces a Hill coefficient (also called the slope factor) to describe the steepness of the curve[4]:

$$E = E_0 + \\frac{E_{max} \\times C^n}{EC_{50}^n + C^n} \\quad \\text{(Equation 2)}$$

When $n = 1$, Equation (2) reduces to the classic hyperbolic model. Values of $n > 1$ produce a steeper, sigmoidal curve; $n < 1$ yields a more gradual relationship. The term "Hill equation" originates from studies of haemoglobin oxygen binding.
"""
doc.add_paragraph(sigmoid_text)

# Add Figure 2
fig2 = generate_figure2()
img_stream = save_matplotlib_as_image(fig2)
doc.add_picture(img_stream, width=Inches(6))
doc.add_paragraph("Figure 2: Sigmoid Emax curves with Hill coefficients of 1, 2 and 4. A higher Hill coefficient increases the steepness of the concentration–effect relationship, meaning a narrower concentration range over which the effect changes rapidly.")

# Add Section 1.3
doc.add_heading('1.3 Logistic Models and Categorical Endpoints', level=2)
logistic_text = """
Not all PD outcomes are continuous. Sedation, pain relief and other clinical endpoints are often recorded on a categorical scale (awake/drowsy/asleep or pain scores). For binary outcomes (success = 1, failure = 0), the probability of success can be modelled using a logistic regression. The logistic transformation ensures that predicted probabilities lie between 0 and 1[6]. If pᵢ is the probability of success for individual i, then:

logit(pᵢ) = ln(pᵢ/(1-pᵢ)) = β₀ + β₁ × Cₑ,ᵢ + εᵢ

where Cₑ,ᵢ is the effect-site concentration, β₀ and β₁ are model coefficients and εᵢ represents residual variability. Logistic models have been used to describe the probability of sedation following midazolam[7] and to determine the concentration at which morphine causes oversedation in children. For ordinal outcomes (e.g. pain scores with multiple categories) a proportional-odds model extends Equation (4) to cumulative probabilities[8].
"""
doc.add_paragraph(logistic_text)

# Add Section 2
doc.add_heading('2. Receptor Theory and Fractional Occupancy', level=1)
receptor_text = """
Classical receptor theory states that a drug exerts its pharmacological effect by binding to a receptor and the effect is proportional to the fraction of receptors occupied. If [R] is the concentration of unbound receptors and [RC] is the concentration of drug–receptor complex, then the fractional occupancy (f) is[9]:

f = [RC]/([R] + [RC]) = C/(Kd + C)

where C is the free drug concentration and Kd is the dissociation constant (the concentration at which half of the receptors are occupied). Rearranging this equation yields the familiar Emax form; when C ≫ Kd almost all receptors are occupied (f ≈ 1) the effect reaches its maximum[10]. The Hill coefficient can be interpreted as the number of binding sites (or degree of cooperativity) and modifies the steepness of the fractional occupancy curve[4].

The receptor binding model is the mechanistic basis for the Emax equation. Equation (5) explains why the concentration–effect relationship is hyperbolic and saturates. It also illustrates the concept of affinity (inverse of Kd) and efficacy (ability of the drug–receptor complex to elicit a response). High-affinity agonists have a low Kd and reach half-maximal effect at low concentrations; partial agonists have reduced efficacy and therefore a lower Eₘₐₓ even when all receptors are occupied.
"""
doc.add_paragraph(receptor_text)

# Add Section 3
doc.add_heading('3. Biophase Distribution and Effect–Compartment Models', level=1)
biophase_text = """
In many situations the observed effect does not occur immediately after changes in plasma concentration. The delay is often due to slow distribution of the drug to its site of action (biophase). When a hysteresis loop is observed by plotting effect against plasma concentration, an effect-compartment model can account for the delay[11]. The model introduces a hypothetical effect site with concentration Cₑ linked to the plasma compartment by a first-order rate constant kₑ₀. The effect site has negligible volume, so the mass balance of the system is unchanged:

dCₑ/dt = kₑ₀ × (Cₚ - Cₑ)

where Cₚ is the plasma concentration. When kₑ₀ is large, equilibration is rapid and the effect is essentially immediate; when kₑ₀ is small, significant delay (up to several hours) occurs. The effect is calculated from Cₑ using an Emax or sigmoid Emax model.
"""
doc.add_paragraph(biophase_text)

# Add Figure 3
fig3 = generate_figure3()
img_stream = save_matplotlib_as_image(fig3)
doc.add_picture(img_stream, width=Inches(6))
doc.add_paragraph("Figure 3: Simulation of a biophase model after an intravenous bolus. (Left) Plasma concentration declines more slowly than the effect-site concentration because kₑ₀ is larger than the elimination rate constant; the effect, calculated via an Emax function, rises and falls following Cₑ. (Right) Plotting the effect against plasma concentration produces a counter-clockwise hysteresis loop characteristic of distributional delay.")

# Add Section 4
doc.add_heading('4. Turnover (Indirect Response) Models', level=1)
turnover_text = """
Many PD responses are controlled by homeostatic processes: endogenous production and loss maintain a constant baseline. Drugs may stimulate or inhibit either the production or the removal of the response variable. To model such systems, indirect response (turnover) models assume the response is generated at a zero-order rate kᵢₙ and removed at a first-order rate kₒᵤₜ. The steady state is R₀ = kᵢₙ/kₒᵤₜ. When a drug alters production or loss, the differential equation becomes[13]:

dR/dt = kᵢₙ - kₒᵤₜ × R

where I(C) and S(C) describe how the drug modulates production or loss. Four basic models (termed Models I–IV) result from combining stimulation or inhibition of either process[13]:
- Model I (inhibition of production): I(C) = 1 - (Iₘₐₓ × C)/(IC₅₀ + C), S(C) = 1.
- Model II (inhibition of loss): I(C) = 1, S(C) = 1 - (Iₘₐₓ × C)/(IC₅₀ + C).
- Model III (stimulation of production): I(C) = 1 + (Sₘₐₓ × C)/(SC₅₀ + C), S(C) = 1.
- Model IV (stimulation of loss): I(C) = 1, S(C) = 1 + (Sₘₐₓ × C)/(SC₅₀ + C).

Here Iₘₐₓ and IC₅₀ are the maximal fractional inhibition and the concentration causing 50% inhibition; Sₘₐₓ and SC₅₀ are the corresponding stimulatory parameters. The area between the effect curve (ABEC) integrates the response over time and is often used to compare treatments[13].
"""
doc.add_paragraph(turnover_text)

# Add Figure 4
fig4 = generate_figure4()
img_stream = save_matplotlib_as_image(fig4)
doc.add_picture(img_stream, width=Inches(6))
doc.add_paragraph("Figure 4: Example of an indirect response model (Model I). A drug inhibits production of a response variable, causing a drop below baseline. As the concentration decays, production recovers and the response returns to steady state.")

# Add Section 5
doc.add_heading('5. Signal Transduction (Transit) Models', level=1)
transit_text = """
Some drug effects involve multiple intermediate steps between receptor activation and the observed response. For example, cytotoxic chemotherapy reduces circulating neutrophils by killing proliferating progenitors in the bone marrow. Because neutrophils must mature through a series of transit compartments before entering the circulation, there is a delay between exposure and nadir cell counts. Transit models introduce a chain of compartments with a common transit rate constant kₜᵣ = n/MTT, where n is the number of transit compartments and MTT is the mean transit time[16]. In a typical myelosuppression model, the proliferative stem cell compartment is multiplied by (1 - E), where E is the fractional cytotoxic effect, and the mature neutrophil compartment receives the output of the final transit compartment.
"""
doc.add_paragraph(transit_text)

# Add Figure 5
fig5 = generate_figure5()
img_stream = save_matplotlib_as_image(fig5)
doc.add_picture(img_stream, width=Inches(6))
doc.add_paragraph("Figure 5: Transit compartment model of chemotherapy-induced neutropenia. A cytotoxic drug inhibits proliferation of precursors. Because cells must pass through several maturation stages (three transit compartments here), the observed circulating count declines with a delay and recovers slowly as the precursor pool regenerates.")

# Add Section 6
doc.add_heading('6. Irreversible Effect Models', level=1)
irreversible_text = """
Some drug actions are essentially irreversible on the time scale of observation. Anticancer drugs kill tumour cells, antibiotics kill bacteria and oxidants convert haemoglobin to methaemoglobin. Irreversible models combine growth and death terms. One form used for bacterial killing is[17]:

dN/dt = k₉ᵣₒwₜₕ × N × (1 - N/Nₘₐₓ) - kₖᵢₗₗ × C × N

where N is the number of cells, k₉ᵣₒwₜₕ is the growth rate, Nₘₐₓ is the carrying capacity and kₖᵢₗₗ is a second-order cell-kill rate constant. The drug effect scales with concentration and cell number; as the population decreases, the killing rate decreases. Irreversible models are essential for antibiotics, antimalarials and anticancer drugs because they can predict bacterial eradication or tumour shrinkage over time. When the drug binds irreversibly to its receptor (e.g. aspirin's acetylation of cyclo-oxygenase), occupancy models must account for receptor turnover rather than assuming equilibrium binding.
"""
doc.add_paragraph(irreversible_text)

# Add Section 7
doc.add_heading('7. Statistical Models for Categorical and Time–to–Event Data', level=1)

# Add Section 7.1
doc.add_heading('7.1 Logistic Regression for Binary Endpoints', level=2)
logistic_binary_text = """
When the response is categorical (e.g. asleep versus awake), logistic regression is the appropriate PD model. The log-odds (logit) of success is modelled as a linear function of concentration and covariates[6]. In the context of PK/PD, the predictor variable is often the effect-site concentration Cₑ. The model coefficients can be estimated by maximum likelihood. Clinically, logistic models have been used to describe the probability that a child receiving midazolam will be sedated (COMFORT-B score > 11) and to identify the concentration of morphine associated with oversedation[7]. They are also useful for dichotomous toxicities such as nausea (yes/no) and seizure occurrence.
"""
doc.add_paragraph(logistic_binary_text)

# Add Section 7.2
doc.add_heading('7.2 Proportional–Odds Models for Ordinal Endpoints', level=2)
proportional_odds_text = """
When outcomes are ordered categories (e.g. pain scores: none, mild, moderate, severe), the probability of being at or above each category can be modelled by a cumulative logit function[8]. If pⱼ is the probability that the response falls in category j or higher, then:

logit(pⱼ) = ln(pⱼ/(1-pⱼ)) = αⱼ - β × Cₑ

where αⱼ is a category-specific intercept and β is the common slope. This framework has been applied to sedation scores, pain scales and clinical global impression scales. The advantage of proportional-odds models is that the same parameters describe the entire ordered scale rather than fitting separate models for each category.
"""
doc.add_paragraph(proportional_odds_text)

# Add Section 7.3
doc.add_heading('7.3 Time–to–Event Models', level=2)
time_to_event_text = """
For events such as onset of anaesthesia, analgesia or adverse effects, the outcome is the time until the event occurs. Cox proportional hazards models or parametric survival models (Weibull, exponential) can be linked to concentration or exposure metrics (e.g. area under the curve). For example, the time to loss of consciousness after propofol administration can be modelled by a hazard that increases with effect-site concentration. Time-to-event models can also incorporate repeated dosing and inter-individual variability.
"""
doc.add_paragraph(time_to_event_text)

# Add Section 8
doc.add_heading('8. Integrating PK and PD: Mechanism–Based Models', level=1)
mechanism_text = """
Modern pharmacometric practice emphasises mechanism-based PD models. These integrate knowledge of receptor binding (fractional occupancy), signal transduction delays, turnover processes and irreversible effects. By linking PK models with PD models in a systems framework, it becomes possible to predict responses under different dosing regimens and patient characteristics. A schematic of a typical mechanism-based model might include:
- PK model for absorption, distribution and elimination (one-, two- or three-compartment).
- Effect compartment describing distribution to the biophase.
- Receptor binding sub-model (fractional occupancy) with parameters Kd, efficacy and Hill coefficient.
- Signal transduction model using transit compartments to capture delays between receptor activation and response.
- Turnover model to account for homeostatic regulation, tolerance and rebound.
- Endpoint model (continuous, categorical, time-to-event) relating the mechanistic signal to the observed clinical outcome.

Mechanism-based models provide several advantages. They allow extrapolation across dosing regimens and populations, identification of biomarkers that precede clinical endpoints and rational optimisation of combination therapy. However, they require rich data and careful assumptions; over-parameterisation and identifiability issues are common.
"""
doc.add_paragraph(mechanism_text)

# Add Section 9
doc.add_heading('9. Clinical Examples and Applications', level=1)

# Add Section 9.1
doc.add_heading('9.1 Sedation and Anaesthesia', level=2)
sedation_text = """
Sedatives such as propofol, midazolam and dexmedetomidine are titrated to achieve adequate hypnosis while avoiding oversedation. PK/PD models for propofol typically include a three-compartment PK model, an effect compartment for brain concentration and a sigmoid inhibitory Emax function linking effect-site concentration to BIS score or probability of loss of consciousness. In a paediatric study, an effect compartment was used to link propofol concentrations to BIS; an inhibitory sigmoid Emax model with two effect compartments captured the relationship between concentration and hypnotic effect[5]. Logistic regression has been applied to binary sedation scores, and proportional-odds models have described ordinal COMFORT scores[18]. Knowledge of the Ce50 (concentration producing 50% probability of sedation) helps clinicians set target concentrations in target-controlled infusion pumps.
"""
doc.add_paragraph(sedation_text)

# Add Section 9.2
doc.add_heading('9.2 Analgesia', level=2)
analgesia_text = """
Analgesics (opioids, NSAIDs, local anaesthetics) often exhibit delayed or indirect effects. Fentanyl analgesia is immediate and can be modelled by a direct Emax model. In contrast, paracetamol (acetaminophen) reduces prostaglandin synthesis, leading to an indirect reduction in pain intensity; Model I or IV turnover models better describe this delay. NSAIDs inhibit cyclo-oxygenase and reduce the production of prostaglandins; the effect on pain relief depends on the turnover of prostaglandins and can be modelled as inhibition of production (Model I).
"""
doc.add_paragraph(analgesia_text)

# Add Section 9.3
doc.add_heading('9.3 Anticoagulation with Warfarin', level=2)
warfarin_text = """
Warfarin inhibits vitamin K–dependent clotting factor synthesis. The PD outcome, INR, does not respond immediately to changes in plasma warfarin concentration because previously synthesised clotting factors continue to circulate. The relationship between S-warfarin concentration and INR was described by an Emax model in a population analysis[3], and a biophase Emax model with transit compartments was favoured because it captured the time delay between concentration and INR response. Clinically, such models aid in predicting INR changes after dose adjustments and in accounting for genetic polymorphisms affecting sensitivity.
"""
doc.add_paragraph(warfarin_text)

# Add Section 9.4
doc.add_heading('9.4 Myelosuppression', level=2)
myelosuppression_text = """
Cytotoxic chemotherapy agents, such as doxorubicin and paclitaxel, cause neutropenia by killing proliferating bone marrow progenitors. The commonly used Friberg transit model employs four transit compartments and an Emax function describing the drug's cytotoxic effect[16]. Parameters include the mean transit time (Mtt), baseline neutrophil count (Circ₀) and the slope of the concentration–effect relationship (often linear). These models predict the timing and extent of neutrophil nadir, allowing dose optimisation and scheduling of colony-stimulating factors.
"""
doc.add_paragraph(myelosuppression_text)

# Add Section 9.5
doc.add_heading('9.5 Antimicrobials and Anticancer Drugs', level=2)
antimicrobials_text = """
Drugs with irreversible actions, such as aminoglycoside antibiotics and many anticancer agents, are modelled by combining growth and kill terms. For example, bacterial population dynamics under antibiotic exposure can be modelled by logistic growth minus concentration-dependent killing[17]. The effect of methemoglobin-forming drugs (e.g. dapsone) can be described by an irreversible model where the fraction of haemoglobin converted to methaemoglobin accumulates over time. Mechanism-based PD models for anticancer drugs may incorporate both reversible receptor binding, irreversible DNA damage and subsequent apoptotic signalling (delay compartments).
"""
doc.add_paragraph(antimicrobials_text)

# Add Section 10
doc.add_heading('10. Summary Tables', level=1)

# Add Table 1
doc.add_heading('Table 1 – Overview of Pharmacodynamic Models', level=2)
table_data = [
    ["Model type", "Mathematical form", "Key parameters", "Typical clinical applications"],
    ["Direct (Emax)", "E = E₀ + (Eₘₐₓ × C)/(EC₅₀ + C)", "Baseline effect E₀; maximum effect Eₘₐₓ; concentration for half-maximal effect EC₅₀", "Antihypertensives, analgesics, sedatives with immediate effect"],
    ["Sigmoid Emax", "E = E₀ + (Eₘₐₓ × Cⁿ)/(EC₅₀ⁿ + Cⁿ)", "Hill coefficient n controlling steepness", "Propofol sedation, neuromuscular blockade"],
    ["Inhibitory Emax", "E = E₀ - (Iₘₐₓ × C)/(IC₅₀ + C)", "Maximal inhibition Iₘₐₓ; IC₅₀", "Warfarin anticoagulation, blood pressure lowering"],
    ["Effect-compartment", "dCₑ/dt = kₑ₀ × (Cₚ - Cₑ)", "Effect-site equilibration constant kₑ₀", "Propofol, opioids, antiemetics with distributional delays"],
    ["Indirect response (Models I–IV)", "dR/dt = kᵢₙ × I(C) - kₒᵤₜ × S(C) × R", "Production rate kᵢₙ; loss rate kₒᵤₜ; maximal inhibition/stimulation Iₘₐₓ/Sₘₐₓ and IC₅₀/SC₅₀", "Warfarin (INR), cortisol suppression, prostaglandin synthesis"],
    ["Precursor–dependent", "Adds precursor pool preceding response", "Precursor pool turnover constant", "Bone resorption markers, hormone synthesis"],
    ["Tolerance", "Slow inhibitory compartment or time-varying kᵢₙ/kₒᵤₜ", "Tolerance rate constant", "Opioid tolerance, tachyphylaxis"],
    ["Transit (signal transduction)", "Chain of compartments: dPᵢ/dt = kₜᵣ × (Pᵢ₋₁ - Pᵢ)", "Mean transit time (Mtt), number of compartments", "Myelosuppression, endocrine responses"],
    ["Irreversible effect", "dN/dt = k₉ᵣₒwₜₕ × N × (1 - N/Nₘₐₓ) - kₖᵢₗₗ × C × N", "Growth rate k₉ᵣₒwₜₕ; kill rate constant kₖᵢₗₗ", "Antibiotics, anticancer agents, methaemoglobin formation"]
]

table = doc.add_table(rows=len(table_data), cols=4)
for i, row in enumerate(table_data):
    for j, cell_text in enumerate(row):
        table.cell(i, j).text = cell_text

# Add Table 2
doc.add_heading('Table 2 – Common Statistical Models for PD Endpoints', level=2)
table_data2 = [
    ["Endpoint type", "Model", "Mathematical description", "Examples"],
    ["Continuous", "Emax or sigmoid Emax; indirect models", "Hyperbolic or sigmoidal functions; turnover equations", "Blood pressure reduction, BIS score, pain intensity"],
    ["Binary", "Logistic regression", "logit(p) = β₀ + β₁ × Cₑ [6]", "Sedation success, nausea occurrence"],
    ["Ordinal categorical", "Proportional-odds (cumulative logit)", "logit(pⱼ) = αⱼ - β × Cₑ [8]", "COMFORT scores, pain scales"],
    ["Time-to-event", "Cox or parametric survival models", "Hazard function depends on concentration or exposure", "Time to loss of consciousness, seizure onset"]
]

table2 = doc.add_table(rows=len(table_data2), cols=4)
for i, row in enumerate(table_data2):
    for j, cell_text in enumerate(row):
        table2.cell(i, j).text = cell_text

# Add Section 11
doc.add_heading('11. Conclusion', level=1)
conclusion_text = """
Pharmacodynamic modelling provides a quantitative framework for linking drug exposure to effect. The Emax model and its sigmoidal variants capture the saturable nature of receptor–mediated responses and form the basis for more complex PD models. Effect-compartment models account for distributional delays when the target site equilibrates slowly with plasma. Turnover models describe systems regulated by production and loss and can incorporate precursor pools and tolerance. Transit models handle multi-step signal transduction and are essential for predicting delayed responses such as myelosuppression. Irreversible effect models combine growth and killing terms to describe antimicrobial and anticancer actions. For categorical and time-to-event endpoints, logistic regression, proportional-odds and survival models provide appropriate frameworks.

Mechanism-based PD models integrate these elements with pharmacokinetic models to improve prediction and understanding of drug action. Clinical applications range from propofol sedation and analgesia to anticoagulation, myelosuppression and antimicrobial therapy. By capturing delays, saturations and feedback mechanisms, these models assist clinicians in dosing optimisation and risk management. As data science and systems pharmacology advance, pharmacodynamic modelling will continue to evolve toward more holistic descriptions of drug–patient interactions.
"""
doc.add_paragraph(conclusion_text)

# Add References
doc.add_heading('References', level=1)
references = """
[1] [2] [4] [9] [10] [21] An overview of pharmacodynamic modelling, ligand-binding approach and its application in clinical practice - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC5355565/

[3] The Clinical Pharmacokinetics and Pharmacodynamics of Warfarin When Combined with Compound Danshen: A Case Study for Combined Treatment of Coronary Heart Diseases with Atrial Fibrillation - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC5702344/

[5] [6] [7] [8] [12] [18] [22] Pharmacokinetic pharmacodynamic modeling of analgesics and sedatives in children - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC10947261/

[11] [14] [16] [17] [19] Mechanism-Based Pharmacodynamic Modeling - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC3684160/

[13] Characteristics of indirect pharmacodynamic models and applications to clinical drug responses - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC1873365/

[15] Basic Concepts in Population Modeling, Simulation, and Model-Based Drug Development: Part 3—Introduction to Pharmacodynamic Modeling Methods - PMC
https://pmc.ncbi.nlm.nih.gov/articles/PMC3917320/

[20] Notes on the Emax model – Notes from a data witch
https://blog.djnavarro.net/posts/2024-01-09_emax-models/
"""
doc.add_paragraph(references)

# Save the document
doc.save('Pharmacodynamic_Models_with_Figures.docx')

print("Word document with figures has been generated: 'Pharmacodynamic_Models_with_Figures.docx'")